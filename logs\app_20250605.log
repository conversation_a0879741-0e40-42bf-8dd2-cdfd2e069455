2025-06-05 09:51:20,215 - app - INFO - Starting <PERSON> Stocks Bot for EGX v1.0.0
2025-06-05 09:51:25,179 - app - INFO - Memory management utilities loaded
2025-06-05 09:51:25,181 - app - INFO - Error handling utilities loaded
2025-06-05 09:51:25,181 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-05 09:51:25,181 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-05 09:51:25,181 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-05 09:51:25,181 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
2025-06-05 09:51:25,181 - app.utils.numpy_fix - INFO - MT19937 is properly registered as a BitGenerator
2025-06-05 09:51:25,188 - app.utils.numpy_fix - WARNING - numpy.random._bit_generator module not found, applying fix
2025-06-05 09:51:25,188 - app.utils.numpy_fix - INFO - Created mock BitGenerator class
2025-06-05 09:51:25,190 - app.utils.numpy_fix - INFO - numpy import fixes applied
2025-06-05 09:51:25,190 - app - INFO - Applied NumPy fix
2025-06-05 09:51:25,192 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 09:51:25,192 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 09:51:25,192 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 09:51:25,194 - app.utils.numpy_bitgenerator_fix - INFO - MT19937 is already a subclass of BitGenerator
2025-06-05 09:51:25,194 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 09:51:25,195 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 09:51:25,196 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 09:51:25,196 - app - INFO - Applied NumPy BitGenerator fix
2025-06-05 09:51:45,039 - app.utils.tensorflow_fix - INFO - TensorFlow imported successfully (version 2.9.1)
2025-06-05 09:51:45,040 - app - INFO - Applied TensorFlow fix
2025-06-05 09:51:45,043 - app.config - INFO - Configuration initialized
2025-06-05 09:51:45,062 - models.train - INFO - TensorFlow version: 2.9.1
2025-06-05 09:51:45,074 - models.train - INFO - TensorFlow test successful
2025-06-05 09:51:49,812 - models.transformer_model - INFO - TensorFlow is available for Transformer model. Version: 2.9.1
2025-06-05 09:51:49,812 - models.train - INFO - Transformer model is available
2025-06-05 09:51:49,812 - models.train - INFO - Using TensorFlow-based models
2025-06-05 09:51:49,814 - models.predict - INFO - Transformer model is available for predictions
2025-06-05 09:51:49,815 - models.predict - INFO - Using TensorFlow-based models for predictions
2025-06-05 09:51:49,820 - app.models.lstm_model - INFO - TensorFlow imported successfully for LSTM model
2025-06-05 09:51:50,329 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 09:51:50,331 - app.utils.numpy_fix_mt19937 - INFO - Applied MT19937 BitGenerator fix
2025-06-05 09:51:50,331 - app.utils.numpy_bitgenerator_fix - INFO - NumPy version: 1.26.4
2025-06-05 09:51:50,333 - app.utils.numpy_bitgenerator_fix - INFO - numpy.random._mt19937 module exists
2025-06-05 09:51:50,334 - app.utils.numpy_bitgenerator_fix - INFO - BitGenerator exists
2025-06-05 09:51:50,335 - app.utils.numpy_bitgenerator_fix - WARNING - MT19937 is not a subclass of BitGenerator, fixing...
2025-06-05 09:51:50,338 - app.utils.numpy_bitgenerator_fix - WARNING - Could not register FixedMT19937: type object 'numpy.random.bit_generator.BitGenerator' has no attribute 'register'
2025-06-05 09:51:50,342 - app.utils.numpy_bitgenerator_fix - INFO - app.utils.numpy_fix module exists, updating FixedMT19937
2025-06-05 09:51:50,343 - app.utils.numpy_bitgenerator_fix - INFO - Updated app.utils.numpy_fix.FixedMT19937
2025-06-05 09:51:50,344 - app.utils.numpy_bitgenerator_fix - INFO - NumPy BitGenerator fix applied successfully
2025-06-05 09:51:50,465 - app.models.hybrid_model - INFO - TensorFlow imported successfully for Hybrid model
2025-06-05 09:51:50,470 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:51:50,885 - app.components.prediction - INFO - Using enhanced prediction module with adaptive model selection
2025-06-05 09:51:51,863 - app.utils.session_state - INFO - Initializing session state
2025-06-05 09:51:51,866 - app.utils.session_state - INFO - Session state initialized
2025-06-05 09:51:53,407 - app - INFO - Found 8 stock files in data/stocks
2025-06-05 09:51:53,421 - app.utils.memory_management - INFO - Memory before cleanup: 426.66 MB
2025-06-05 09:51:53,635 - app.utils.memory_management - INFO - Garbage collection: collected 14 objects
2025-06-05 09:51:53,642 - app.utils.memory_management - INFO - Memory after cleanup: 426.66 MB (freed -0.00 MB)
2025-06-05 09:52:15,215 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:52:15,264 - app.utils.memory_management - INFO - Memory before cleanup: 430.45 MB
2025-06-05 09:52:15,584 - app.utils.memory_management - INFO - Garbage collection: collected 0 objects
2025-06-05 09:52:15,586 - app.utils.memory_management - INFO - Memory after cleanup: 430.45 MB (freed 0.00 MB)
2025-06-05 09:52:17,550 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:52:17,600 - app.utils.memory_management - INFO - Memory before cleanup: 431.38 MB
2025-06-05 09:52:17,859 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-05 09:52:17,859 - app.utils.memory_management - INFO - Memory after cleanup: 431.42 MB (freed -0.04 MB)
2025-06-05 09:52:21,780 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:52:21,831 - app.utils.memory_management - INFO - Memory before cleanup: 431.43 MB
2025-06-05 09:52:22,103 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-05 09:52:22,104 - app.utils.memory_management - INFO - Memory after cleanup: 431.43 MB (freed 0.00 MB)
2025-06-05 09:52:23,230 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:52:42,489 - app.utils.session_state - ERROR - Error tracked: app_crash - Expanders may not be nested inside other expanders.
2025-06-05 09:52:42,495 - app - ERROR - Application crashed: Expanders may not be nested inside other expanders.
2025-06-05 09:52:42,499 - app.utils.memory_management - INFO - Memory before cleanup: 432.78 MB
2025-06-05 09:52:42,738 - app.utils.memory_management - INFO - Garbage collection: collected 183 objects
2025-06-05 09:52:42,740 - app.utils.memory_management - INFO - Memory after cleanup: 432.78 MB (freed 0.00 MB)
2025-06-05 09:52:42,743 - app.utils.memory_management - INFO - Memory before cleanup: 432.78 MB
2025-06-05 09:52:43,030 - app.utils.memory_management - INFO - Garbage collection: collected 25 objects
2025-06-05 09:52:43,032 - app.utils.memory_management - INFO - Memory after cleanup: 432.78 MB (freed 0.00 MB)
2025-06-05 09:52:59,810 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:52:59,859 - app.utils.memory_management - INFO - Memory before cleanup: 433.45 MB
2025-06-05 09:53:00,156 - app.utils.memory_management - INFO - Garbage collection: collected 211 objects
2025-06-05 09:53:00,156 - app.utils.memory_management - INFO - Memory after cleanup: 433.45 MB (freed 0.00 MB)
2025-06-05 09:53:06,982 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:53:07,012 - app.utils.memory_management - INFO - Memory before cleanup: 433.45 MB
2025-06-05 09:53:07,223 - app.utils.memory_management - INFO - Garbage collection: collected 182 objects
2025-06-05 09:53:07,223 - app.utils.memory_management - INFO - Memory after cleanup: 433.45 MB (freed 0.00 MB)
2025-06-05 09:53:08,621 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:53:08,675 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 09:53:08,678 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 09:53:08,680 - app - INFO - Data shape: (585, 36)
2025-06-05 09:53:08,685 - app - INFO - File COMI contains 2025 data
2025-06-05 09:53:08,725 - app - INFO - Feature engineering for COMI completed in 0.03 seconds
2025-06-05 09:53:08,729 - app - INFO - Features shape: (585, 36)
2025-06-05 09:53:08,758 - app - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 09:53:08,763 - app - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 09:53:08,765 - app - INFO - Data shape: (585, 36)
2025-06-05 09:53:08,767 - app - INFO - File COMI contains 2025 data
2025-06-05 09:53:08,772 - app.utils.memory_management - INFO - Memory before cleanup: 436.60 MB
2025-06-05 09:53:08,969 - app.utils.memory_management - INFO - Garbage collection: collected 286 objects
2025-06-05 09:53:08,969 - app.utils.memory_management - INFO - Memory after cleanup: 436.60 MB (freed 0.00 MB)
2025-06-05 09:53:09,157 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:53:09,250 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 09:53:09,298 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.02 seconds
2025-06-05 09:53:09,300 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 09:53:09,302 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 09:53:09,306 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 09:53:09,435 - app.utils.memory_management - INFO - Memory before cleanup: 436.59 MB
2025-06-05 09:53:09,722 - app.utils.memory_management - INFO - Garbage collection: collected 112 objects
2025-06-05 09:53:09,725 - app.utils.memory_management - INFO - Memory after cleanup: 436.59 MB (freed 0.00 MB)
2025-06-05 09:53:13,424 - app - INFO - Using TensorFlow-based LSTM model
2025-06-05 09:53:15,530 - app.utils.common - INFO - Found 8 stock files in data/stocks
2025-06-05 09:53:15,566 - app.utils.common - INFO - Loaded stock data for COMI from data/stocks\COMI.csv in 0.01 seconds
2025-06-05 09:53:15,570 - app.utils.common - INFO - Date range: 2023-01-02 to 2025-06-04
2025-06-05 09:53:15,570 - app.utils.common - INFO - Data shape: (585, 36)
2025-06-05 09:53:15,571 - app.utils.common - INFO - File COMI contains 2025 data
2025-06-05 09:53:15,632 - app.utils.memory_management - INFO - Memory before cleanup: 436.86 MB
2025-06-05 09:53:15,838 - app.utils.memory_management - INFO - Garbage collection: collected 297 objects
2025-06-05 09:53:15,840 - app.utils.memory_management - INFO - Memory after cleanup: 436.86 MB (freed 0.00 MB)
2025-06-05 10:03:42,616 - app - INFO - Starting AI Stocks Bot for EGX v1.0.0
2025-06-05 10:03:42,622 - app - INFO - Memory management utilities loaded
2025-06-05 10:03:42,626 - app - INFO - Error handling utilities loaded
2025-06-05 10:03:42,628 - app.utils.numpy_fix - INFO - numpy._core module exists, no fix needed
2025-06-05 10:03:42,630 - app.utils.numpy_fix - INFO - numpy._core.multiarray module exists, no fix needed
2025-06-05 10:03:42,631 - app.utils.numpy_fix - INFO - numpy._core._multiarray_umath module exists, no fix needed
2025-06-05 10:03:42,633 - app.utils.numpy_fix - INFO - numpy.random._mt19937 module exists, no fix needed
