"""
Enhanced TradingView chart integration using the professional TradingView Charting Library
"""
import streamlit as st
import logging
import uuid
import json
import os
from typing import List, Optional, Dict, Any

# Configure logging
logger = logging.getLogger(__name__)

# TradingView Professional Library Configuration
TRADINGVIEW_LIBRARY_PATH = "static/tradingview/"
TRADINGVIEW_DATAFEED_URL = "http://127.0.0.1:8000/api/tradingview/"

def google_charts_professional_component(symbol: str, width: int = 1000, height: int = 600,
                                        theme: str = "dark", interval: str = "1D",
                                        container_id: str = None,
                                        studies: List[str] = None):
    """
    Professional stock chart using Google Charts with candlestick visualization

    This provides a clean, fast, and reliable charting experience with:
    - Interactive candlestick charts
    - Zoom and pan functionality
    - Professional styling
    - Real stock data visualization
    - No server dependencies
    - Fast loading

    Args:
        symbol (str): Stock symbol (e.g., "COMI" for Commercial International Bank)
        width (int): Chart width in pixels
        height (int): Chart height in pixels
        theme (str): Chart theme ("dark" or "light")
        interval (str): Chart interval (currently supports daily data)
        container_id (str): HTML container ID (auto-generated if None)
        studies (List[str]): Technical studies to add (future enhancement)
    """

    # Generate unique container ID if not provided
    if container_id is None:
        container_id = f"google_chart_{uuid.uuid4().hex[:8]}"

    # Clean and format symbol
    clean_symbol = symbol.replace(" ", "").replace("EGX:", "").replace("EGX-", "")

    try:
        # Load stock data for the chart
        from app.utils.data_loader import load_stock_data
        stock_data = load_stock_data(clean_symbol)

        if stock_data is None or stock_data.empty:
            st.error(f"❌ No data available for symbol: {clean_symbol}")
            st.info("💡 Please ensure the stock data is available in the data folder.")
            return None

        # Prepare data for Google Charts (last 100 data points for performance)
        chart_data = stock_data.tail(100).copy()
        chart_data = chart_data.reset_index()

        # Convert data to Google Charts format
        chart_data_json = []
        for _, row in chart_data.iterrows():
            chart_data_json.append([
                row['Date'].strftime('%Y-%m-%d'),
                float(row['Low']),
                float(row['Open']),
                float(row['Close']),
                float(row['High'])
            ])

        # Theme configuration
        bg_color = '#1e1e1e' if theme == 'dark' else '#ffffff'
        text_color = '#ffffff' if theme == 'dark' else '#333333'
        grid_color = '#333333' if theme == 'dark' else '#e1e1e1'

        # Calculate price statistics
        latest_price = float(chart_data.iloc[-1]['Close'])
        prev_price = float(chart_data.iloc[-2]['Close']) if len(chart_data) > 1 else latest_price
        price_change = latest_price - prev_price
        price_change_pct = (price_change / prev_price * 100) if prev_price != 0 else 0

        # Display metrics before chart
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("📊 Symbol", clean_symbol)
        with col2:
            st.metric("💰 Latest Price", f"{latest_price:.2f} EGP")
        with col3:
            st.metric("📈 Change", f"{price_change:+.2f} EGP", f"{price_change_pct:+.1f}%")
        with col4:
            st.metric("📅 Data Points", len(chart_data))

        # Create the Google Charts HTML
        google_charts_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
            <style>
                body {{
                    margin: 0;
                    padding: 10px;
                    background-color: {bg_color};
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                }}
                #{container_id} {{
                    width: {width}px;
                    height: {height}px;
                    border: 1px solid {grid_color};
                    border-radius: 8px;
                    background-color: {bg_color};
                }}
                .chart-header {{
                    color: {text_color};
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 10px;
                    text-align: center;
                }}
                .chart-info {{
                    color: {text_color};
                    font-size: 12px;
                    margin-top: 10px;
                    text-align: center;
                }}
                .loading {{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: {height}px;
                    color: {text_color};
                    font-size: 16px;
                }}
            </style>
        </head>
        <body>
            <div class="chart-header">📊 {clean_symbol} - Professional Stock Chart</div>
            <div id="{container_id}">
                <div class="loading">🔄 Loading Professional Chart...</div>
            </div>
            <div class="chart-info">
                📈 Candlestick Chart | 🕒 Last {len(chart_data)} periods | 🎨 Interactive & Zoomable
            </div>

            <script type="text/javascript">
                google.charts.load('current', {{'packages':['corechart']}});
                google.charts.setOnLoadCallback(drawChart);

                function drawChart() {{
                    try {{
                        var data = google.visualization.arrayToDataTable([
                            ['Date', 'Low', 'Open', 'Close', 'High'],
                            {str(chart_data_json).replace("'", "")}
                        ]);

                        var options = {{
                            title: '{clean_symbol} Stock Price',
                            titleTextStyle: {{
                                color: '{text_color}',
                                fontSize: 16,
                                bold: true
                            }},
                            backgroundColor: '{bg_color}',
                            legend: {{
                                position: 'top',
                                alignment: 'center',
                                textStyle: {{
                                    color: '{text_color}',
                                    fontSize: 12
                                }}
                            }},
                            candlestick: {{
                                fallingColor: {{ strokeWidth: 0, fill: '#ff6b6b' }},
                                risingColor: {{ strokeWidth: 0, fill: '#4ecdc4' }}
                            }},
                            hAxis: {{
                                title: 'Date',
                                titleTextStyle: {{
                                    color: '{text_color}',
                                    fontSize: 14
                                }},
                                textStyle: {{
                                    color: '{text_color}',
                                    fontSize: 11
                                }},
                                gridlines: {{
                                    color: '{grid_color}',
                                    count: 8
                                }},
                                minorGridlines: {{
                                    color: 'transparent'
                                }}
                            }},
                            vAxis: {{
                                title: 'Price (EGP)',
                                titleTextStyle: {{
                                    color: '{text_color}',
                                    fontSize: 14
                                }},
                                textStyle: {{
                                    color: '{text_color}',
                                    fontSize: 11
                                }},
                                gridlines: {{
                                    color: '{grid_color}',
                                    count: 6
                                }},
                                minorGridlines: {{
                                    color: 'transparent'
                                }},
                                format: '#.##'
                            }},
                            chartArea: {{
                                left: 80,
                                top: 60,
                                width: '{width-120}',
                                height: '{height-120}',
                                backgroundColor: '{bg_color}'
                            }},
                            explorer: {{
                                actions: ['dragToZoom', 'rightClickToReset'],
                                axis: 'horizontal',
                                keepInBounds: true,
                                maxZoomIn: 4.0
                            }},
                            crosshair: {{
                                trigger: 'both',
                                orientation: 'both',
                                color: '{text_color}',
                                opacity: 0.5
                            }},
                            focusTarget: 'category',
                            tooltip: {{
                                isHtml: true,
                                textStyle: {{
                                    color: '{text_color}',
                                    fontSize: 12
                                }}
                            }}
                        }};

                        var chart = new google.visualization.CandlestickChart(document.getElementById('{container_id}'));

                        // Add event listeners for interactivity
                        google.visualization.events.addListener(chart, 'select', function() {{
                            var selection = chart.getSelection();
                            if (selection.length > 0) {{
                                var row = selection[0].row;
                                var date = data.getValue(row, 0);
                                var open = data.getValue(row, 2);
                                var close = data.getValue(row, 3);
                                var high = data.getValue(row, 4);
                                var low = data.getValue(row, 1);

                                console.log('Selected data point:', {{
                                    date: date,
                                    open: open,
                                    close: close,
                                    high: high,
                                    low: low
                                }});
                            }}
                        }});

                        chart.draw(data, options);

                        console.log('Google Charts professional chart loaded successfully');

                    }} catch (error) {{
                        console.error('Error drawing chart:', error);
                        document.getElementById('{container_id}').innerHTML =
                            '<div style="color: #ff6b6b; text-align: center; padding: 50px;">❌ Failed to load chart<br>Error: ' + error.message + '</div>';
                    }}
                }}

                // Redraw chart on window resize
                window.addEventListener('resize', function() {{
                    drawChart();
                }});
            </script>
        </body>
        </html>
        """

        # Render the Google Charts component
        st.components.v1.html(google_charts_html, height=height + 100)

        # Add professional chart info
        st.caption("🚀 **Professional Google Charts** - Interactive candlestick chart with zoom, pan, and crosshair features")

        return {
            "symbol": clean_symbol,
            "container_id": container_id,
            "theme": theme,
            "data_points": len(chart_data)
        }

    except Exception as e:
        st.error(f"❌ Error creating Google Charts component: {str(e)}")
        st.info("💡 Please check that the stock data is available and properly formatted.")
        return None

def tradingview_chart_component(symbol: str, width: int = 1000, height: int = 600,
                               theme: str = "dark", interval: str = "D",
                               style: str = "1",
                               studies: Optional[List[str]] = None):
    """
    Embed a TradingView chart widget in Streamlit using the external embedding approach

    Args:
        symbol (str): Stock symbol (e.g., "EGX:COMI" for Commercial International Bank)
        width (int): Width of the chart in pixels
        height (int): Height of the chart in pixels
        theme (str): Chart theme ("light" or "dark")
        interval (str): Chart interval (e.g., "D" for daily, "W" for weekly, "M" for monthly)
        style (str): Chart style ("1" for bars, "2" for candles, "3" for line, "4" for area)
        studies (List[str]): List of studies to add to the chart
    """
    # Try different symbol formats for TradingView
    # For EGX stocks, we need to try different prefixes

    # First, clean up the symbol
    clean_symbol = symbol.replace(" ", "")
    if ":" in clean_symbol:
        # Extract just the symbol part if it has a prefix
        clean_symbol = clean_symbol.split(":")[1]

    # Try different formats for EGX stocks
    # Format 1: No prefix (just the symbol)
    formatted_symbol = clean_symbol

    # Debug info
    print(f"Regular Chart: Original symbol: {symbol}, Formatted for TradingView: {formatted_symbol}")

    # Map style numbers to names for the external widget
    style_map = {
        "1": "BARS",
        "2": "CANDLES",
        "3": "LINE",
        "4": "AREA"
    }
    chart_style = style_map.get(style, "CANDLES")

    # Create the TradingView widget HTML using the direct JavaScript API
    # This is more reliable for EGX stocks
    html_content = f"""
    <!-- TradingView Widget BEGIN -->
    <div class="tradingview-widget-container" style="height:{height}px;width:{width}px">
      <div id="tradingview_chart_main" style="height:100%;width:100%"></div>
      <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
      <script type="text/javascript">
      new TradingView.widget(
      {{
        "width": {width},
        "height": {height},
        "symbol": "EGX:{formatted_symbol}",
        "interval": "D",
        "timezone": "exchange",
        "theme": "{theme}",
        "style": "1",
        "locale": "en",
        "toolbar_bg": "#f1f3f6",
        "enable_publishing": false,
        "hide_top_toolbar": false,
        "hide_side_toolbar": false,
        "allow_symbol_change": true,
        "container_id": "tradingview_chart_main"
      }}
      );
      </script>
    </div>
    <!-- TradingView Widget END -->
    """

    # Display the TradingView widget
    st.components.v1.html(html_content, width=width, height=height)

    # Add a note about troubleshooting
    st.caption("If the chart appears black, try switching to a different tab and back, or refresh the page.")

    return formatted_symbol

def tradingview_advanced_chart_component(symbol: str, width: int = 1000, height: int = 600):
    """
    Embed a TradingView Advanced Chart widget in Streamlit using the external embedding approach

    Args:
        symbol (str): Stock symbol (e.g., "EGX:COMI" for Commercial International Bank)
        width (int): Width of the chart in pixels
        height (int): Height of the chart in pixels
    """
    # Try different symbol formats for TradingView
    # For EGX stocks, we need to try different prefixes

    # First, clean up the symbol
    clean_symbol = symbol.replace(" ", "")
    if ":" in clean_symbol:
        # Extract just the symbol part if it has a prefix
        clean_symbol = clean_symbol.split(":")[1]

    # Try different formats for EGX stocks
    # Format 1: No prefix (just the symbol)
    formatted_symbol = clean_symbol

    # Debug info
    print(f"Advanced Chart: Original symbol: {symbol}, Formatted for TradingView: {formatted_symbol}")

    # Create the TradingView Advanced Chart widget HTML using the external embedding approach
    # Use the standard chart widget with proper EGX prefix
    html_content = f"""
    <!-- TradingView Widget BEGIN -->
    <div class="tradingview-widget-container" style="height:{height}px;width:{width}px">
      <div id="tradingview_chart" style="height:100%;width:100%"></div>
      <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
      <script type="text/javascript">
      new TradingView.widget(
      {{
        "width": {width},
        "height": {height},
        "symbol": "EGX-{formatted_symbol}",
        "interval": "D",
        "timezone": "exchange",
        "theme": "dark",
        "style": "1",
        "locale": "en",
        "toolbar_bg": "#f1f3f6",
        "enable_publishing": false,
        "hide_top_toolbar": false,
        "hide_side_toolbar": false,
        "allow_symbol_change": true,
        "container_id": "tradingview_chart"
      }}
      );
      </script>
    </div>
    <!-- TradingView Widget END -->
    """

    # Display the TradingView widget
    st.components.v1.html(html_content, width=width, height=height)

    # Add a note about troubleshooting
    st.caption("If the chart appears black, try switching to a different tab and back, or refresh the page.")

    return formatted_symbol

def tradingview_mini_chart_component(symbol: str, width: int = 350, height: int = 220,
                                    theme: str = "dark"):
    """
    Embed a TradingView Mini Chart widget in Streamlit using the external embedding approach

    Args:
        symbol (str): Stock symbol (e.g., "EGX:COMI" for Commercial International Bank)
        width (int): Width of the chart in pixels
        height (int): Height of the chart in pixels
        theme (str): Chart theme ("light" or "dark")
    """
    # First, clean up the symbol
    clean_symbol = symbol.replace(" ", "")
    if ":" in clean_symbol:
        # Extract just the symbol part if it has a prefix
        clean_symbol = clean_symbol.split(":")[1]

    # Instead of using the mini-symbol-overview widget, let's use the single-ticker widget
    # which has better compatibility with international stocks
    html_content = f"""
    <!-- TradingView Widget BEGIN -->
    <div class="tradingview-widget-container" style="height:{height}px;width:{width}px">
      <div class="tradingview-widget-container__widget" style="height:100%;width:100%"></div>
      <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-single-quote.js" async>
      {{
        "symbol": "EGX:{clean_symbol}",
        "width": {width},
        "height": {height},
        "locale": "en",
        "colorTheme": "{theme}",
        "isTransparent": false
      }}
      </script>
    </div>
    <!-- TradingView Widget END -->
    """

    # Display the TradingView widget
    st.components.v1.html(html_content, width=width, height=height)

    # Return the formatted symbol (EGX:SYMBOL format)
    return f"EGX:{clean_symbol}"
